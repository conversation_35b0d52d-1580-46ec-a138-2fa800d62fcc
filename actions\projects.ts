"use server";

import db from "@/lib/db";
import { ActionState, StatusCode } from "@/lib/types";
import {
  createProject,
  deleteProject,
  isProject,
  updateProjectTitle,
} from "@/queries/projects";
import { auth } from "@clerk/nextjs/server";
import { Project } from "@prisma/client";
import { revalidatePath } from "next/cache";

type CreateProjectProps = {
  title: Project["title"];
};

export type UpdateProjectTitleProps = Pick<Project, "id" | "title">;

export const createProjectAction = async ({
  title,
}: CreateProjectProps): Promise<ActionState<Project>> => {
  const { userId } = await auth();
  try {
    if (!userId) {
      return {
        code: StatusCode.Unauthorized,
        message: "You are not authorized to create a project",
        success: false,
      };
    }

    const project = await createProject(title, userId);
    return {
      code: StatusCode.Created,
      message: "Project created successfully",
      data: project as Project,
      success: true,
    };
  } catch (error) {
    console.error("Error creating project:", error);
    return {
      code: StatusCode.InternalServerError,
      message: "Something went wrong while creating the project",
      error: error as Error,
      success: false,
    };
  } finally {
    revalidatePath("/project");
  }
};

export const updateProjectTitleAction = async ({
  title,
  id,
}: UpdateProjectTitleProps): Promise<ActionState<Project>> => {
  const { userId } = await auth();
  try {
    if (!userId) {
      return {
        code: StatusCode.Unauthorized,
        message: "You are not authorized to update a project",
        success: false,
      };
    }
    const isProjectExists = await isProject(id);

    if (!isProjectExists) {
      return {
        code: StatusCode.NotFound,
        message: "Project not found",
        success: false,
      };
    }

    const project = await updateProjectTitle(title, id);
    return {
      code: StatusCode.Ok,
      message: "Project title updated successfully",
      data: project as Project,
      success: true,
    };
  } catch (error) {
    console.error("Error updating project title:", error);
    return {
      code: StatusCode.InternalServerError,
      message: "Something went wrong while updating the project title",
      error: error as Error,
      success: false,
    };
  } finally {
    revalidatePath("/project");
  }
};

export const deleteProjectAction = async (
  id: Project["id"]
): Promise<ActionState<Project>> => {
  const { userId } = await auth();
  try {
    if (!userId) {
      return {
        code: StatusCode.Unauthorized,
        message: "You are not authorized to delete a project",
        success: false,
      };
    }

    const isProjectExists = await isProject(id);

    if (!isProjectExists) {
      return {
        code: StatusCode.NotFound,
        message: "Project not found",
        success: false,
      };
    }

    const project = await deleteProject(id);

    return {
      code: StatusCode.Ok,
      data: project as Project,
      message: "Project deleted successfully",
      success: true,
    };
  } catch (error) {
    console.error("Error deleting project:", error);
    return {
      code: StatusCode.InternalServerError,
      message: "Something went wrong while deleting the project",
      error: error as Error,
      success: false,
    };
  } finally {
    revalidatePath("/project");
  }
};

export const getProjectOfUser = async (userId: string) => {
  const project = await db.project.findMany({
    where: {
      userId,
    },
    orderBy: {
      createdAt: "desc",
    },
    include: {
      user: true,
    },
  });

  return project;
};

export const getProject = async (id: string, userId: string) => {
  const project = await db.project.findUnique({
    where: {
      id,
      userId,
    },
    include: {
      user: true,
      sections: {
        include: {
          text: {
            include: {
              externalLink: true,
            },
          },
          image: true,
          externalLink: true,
        },
        orderBy: {
          index: "asc",
        },
      },
    },
  });

  return project;
};
