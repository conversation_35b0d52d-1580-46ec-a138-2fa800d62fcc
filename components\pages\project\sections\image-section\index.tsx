"use client";

import React from "react";
import Image from "next/image";
import { Section, Image as Images } from "@prisma/client";
import { Button } from "@/components/ui/button";
import ImageForm from "./image-form";
import { cn } from "@/lib/utils";
import { Edit2Icon } from "lucide-react";

type Props = {
  section: Section & {
    image?: Images | null;
  };
};

const ImageSection = ({ section }: Props) => {
  if (!section.image) {
    return null;
  }

  return (
    <figure className="relative flex flex-col items-center group/section">
      <Image
        src={section.image?.src as string}
        alt={section.image?.alt as string}
        width={section.image?.width ? Number(section.image.width) : 500}
        height={section.image?.height ? Number(section.image.height) : 500}
        className={cn(
          section.image?.width && section.image?.height
            ? `w-[${section.image?.width}]! h-[${section.image?.height}]!`
            : "w-full h-full min-w-0 min-h-0"
        )}
      />

      {section.image?.caption && (
        <figcaption className="text-sm text-muted-foreground mt-8">
          {section.image?.caption}
        </figcaption>
      )}

      {/* Overlay on hover */}
      <ImageForm sectionId={section.id} image={section.image}>
        <div className="absolute inset-0 hover:bg-black/40 group-hover/section:opacity-100 transition-colors duration-300 flex items-center justify-center">
          <Button
            variant="secondary"
            size="sm"
            className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 gap-2"
          >
            <Edit2Icon className="w-4 h-4" />
            Edit Image
          </Button>
        </div>
      </ImageForm>
    </figure>
  );
};

export default ImageSection;
