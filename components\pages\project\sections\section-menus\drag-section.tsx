import React from "react";
import { Section } from "@prisma/client";
import { But<PERSON> } from "@/components/ui/button";
import { Move } from "lucide-react";

type Props = {
  section: Section;
};

const DragSection = (props: Props) => {
  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 p-0 hover:bg-muted cursor-grab active:cursor-grabbing"
        title="Drag to reorder"
      >
        <Move className="h-4 w-4" />
      </Button>
    </>
  );
};

export default DragSection;
