"use server";

import {
  createSection,
  deleteSection,
  isSection,
  updateSectionTitle,
} from "@/queries/sections";
import { ActionState, StatusCode } from "@/lib/types";
import { Section } from "@prisma/client";
import { revalidatePath } from "next/cache";

type CreateSectionProps = {
  projectId: Section["projectId"];
  index: Section["index"];
  type: Section["type"];
};

export const createSectionAction = async ({
  projectId,
  index,
  type,
}: CreateSectionProps): Promise<ActionState<Section>> => {
  try {
    const section = await createSection({
      projectId,
      index,
      type,
    });

    return {
      code: StatusCode.Created,
      message: "Section created successfully",
      data: section,
      success: true,
    };
  } catch (error) {
    console.error("Error creating section:", error);
    return {
      code: StatusCode.InternalServerError,
      message: "Something went wrong while creating the section",
      error: error as Error,
      success: false,
    };
  } finally {
    revalidatePath(`/project/${projectId}`);
  }
};

//delete setion action

export type DeleteSectionProps = Pick<Section, "id" | "projectId" | "index">;

export const deleteSectionAction = async (
  data: DeleteSectionProps
): Promise<ActionState<Section>> => {
  try {
    const sectionExists = await isSection(data.id);

    if (!sectionExists) {
      return {
        code: StatusCode.NotFound,
        message: "Section not found",
        success: false,
      };
    }

    const section = await deleteSection(data);

    return {
      code: StatusCode.Ok,
      message: "Section deleted successfully",
      data: section,
      success: true,
    };
  } catch (error) {
    console.error("Error deleting section:", error);
    return {
      code: StatusCode.InternalServerError,
      message: "Something went wrong while deleting the section",
      error: error as Error,
      success: false,
    };
  } finally {
    revalidatePath(`/project/${data.projectId}`);
  }
};

export type UpdateSectionTitleProps = Pick<Section, "id" | "title">;

export const updateSectionTitleAction = async ({
  id,
  title,
}: UpdateSectionTitleProps): Promise<ActionState<Section>> => {
  let sectionExists;
  try {
    sectionExists = await isSection(id);

    if (!sectionExists) {
      return {
        code: StatusCode.NotFound,
        message: "Section not found",
        success: false,
      };
    }
    const section = await updateSectionTitle({
      id,
      title,
    });

    return {
      code: StatusCode.Ok,
      message: "Section title updated successfully",
      data: section,
      success: true,
    };
  } catch (error) {
    console.error("Error updating section title:", error);
    return {
      code: StatusCode.InternalServerError,
      message: "Something went wrong while updating the section title",
      error: error as Error,
      success: false,
    };
  } finally {
    revalidatePath(`/project/${sectionExists?.projectId}`);
  }
};
