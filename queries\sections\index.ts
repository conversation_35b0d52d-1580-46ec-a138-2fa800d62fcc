"use server";

import db from "@/lib/db";

export const createSection = async (data: any) => {
  try {
    const section = await db.$transaction(async (tx) => {
      await tx.section.updateMany({
        where: {
          projectId: data.projectId,
          index: {
            gte: data.index,
          },
        },
        data: {
          index: {
            increment: 1,
          },
        },
      });

      const newSection = await tx.section.create({
        data: {
          projectId: data.projectId,
          index: +data.index,
          type: data.type,
        },
      });

      if (data.type === "TEXT") {
        await tx.text.create({
          data: {
            sectionId: newSection.id,
          },
        });
      }

      if (data.type === "IMAGE") {
        await tx.image.create({
          data: {
            sectionId: newSection.id,
          },
        });
      }

      if (data.type === "TEXTIMAGE") {
        await tx.text.create({
          data: {
            sectionId: newSection.id,
          },
        });

        await tx.image.create({
          data: {
            sectionId: newSection.id,
          },
        });
      }

      return newSection;
    });

    return section;
  } catch (error) {
    console.error("Error creating section:", error);
    return;
  }
};

//delete section
export const deleteSection = async (section: any) => {
  try {
    const sections = await db.$transaction(async (tx) => {
      await tx.section.updateMany({
        where: {
          projectId: section.projectId,
          index: {
            gte: section.index,
          },
        },
        data: {
          index: {
            decrement: 1,
          },
        },
      });

      return await tx.section.delete({
        where: {
          id: section.id,
        },
      });
    });

    return sections;
  } catch (error) {
    console.error("Error deleting section:", error);
    return;
  }
};

export const isSection = async (id: string) => {
  const section = await db.section.findUnique({
    where: {
      id,
    },
  });

  return section;
};

export const updateSectionTitle = async (data: any) => {
  try {
    const section = await db.section.update({
      where: {
        id: data.id,
      },
      data: {
        title: data.title,
      },
    });

    return section;
  } catch (error) {
    console.error("Error updating section title:", error);
    return;
  }
};
