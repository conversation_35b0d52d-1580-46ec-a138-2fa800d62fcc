"use server";

import { ActionState, StatusCode } from "@/lib/types";
import { createImage, isImage, updateImage } from "@/queries/image";
import { Image } from "@prisma/client";
import { revalidatePath } from "next/cache";

export type CreateImageProps = {
  sectionId: Image["sectionId"];
  src?: Image["src"];
  alt?: Image["alt"];
  caption?: Image["caption"];
  width?: Image["width"];
  height?: Image["height"];
};

export type UpdateImageProps = {
  id: Image["id"];
  src?: Image["src"];
  alt?: Image["alt"];
  caption?: Image["caption"];
  width?: Image["width"];
  height?: Image["height"];
};

export const createImageAction = async ({
  sectionId,
  src,
  alt,
  caption,
  width,
  height,
}: CreateImageProps): Promise<ActionState<Image>> => {
  let image;
  try {
    image = await createImage({ sectionId, src, alt, caption, width, height });
    return {
      code: StatusCode.Created,
      message: "Image created successfully",
      data: image as Image,
      success: true,
    };
  } catch (error) {
    console.error("Error creating image:", error);
    return {
      code: StatusCode.InternalServerError,
      message: "Something went wrong while creating the image",
      error: error as Error,
      success: false,
    };
  } finally {
    revalidatePath(`/project/${image?.section?.projectId}`);
  }
};

export const updateImageAction = async ({
  id,
  src,
  alt,
  caption,
  width,
  height,
}: UpdateImageProps): Promise<ActionState<Image>> => {
  let image;
  try {
    const isImageExists = await isImage(id);

    if (!isImageExists) {
      return {
        code: StatusCode.NotFound,
        message: "Image not found",
        success: false,
      };
    }

    image = await updateImage({ id, src, alt, caption, width, height });
    return {
      code: StatusCode.Ok,
      message: "Image updated successfully",
      data: image as Image,
      success: true,
    };
  } catch (error) {
    console.error("Error updating image:", error);
    return {
      code: StatusCode.InternalServerError,
      message: "Something went wrong while updating the image",
      error: error as Error,
      success: false,
    };
  } finally {
    revalidatePath(`/project/${image?.section?.projectId}`);
  }
};
