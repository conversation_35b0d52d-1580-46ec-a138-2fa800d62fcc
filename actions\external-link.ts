"use server";

import { ActionState, StatusCode } from "@/lib/types";
import {
  createExternalSectionLink,
  createExternalTextLink,
  deleteExternalLink,
  isExternalLink,
  updateExternalLink,
} from "@/queries/external-link";
import { ExternalLink } from "@prisma/client";
import { revalidatePath } from "next/cache";

export type CreateExternalSectionLinkProps = {
  sectionId: string;
  label?: string;
  url?: string;
};

export type CreateExternalTextLinkProps = {
  textId: string;
  label?: string;
  url?: string;
};

export type UpdateExternalLinkProps = {
  id: string;
  label: string;
  url: string;
};

export const createExternalSectionLinkAction = async ({
  sectionId,
  label,
  url,
}: CreateExternalSectionLinkProps): Promise<ActionState<ExternalLink>> => {
  let externalLink: any = null;

  try {
    externalLink = await createExternalSectionLink({
      sectionId,
      label,
      url,
    });

    return {
      code: StatusCode.Created,
      message: "External link created successfully",
      data: externalLink as ExternalLink,
      success: true,
    };
  } catch (error) {
    console.error("Error creating external link:", error);
    return {
      code: StatusCode.InternalServerError,
      message: "Something went wrong while creating the external link",
      error: error as Error,
      success: false,
    };
  } finally {
    revalidatePath(`/project/${externalLink?.section?.projectId}`);
  }
};

export const createExternalTextLinkAction = async ({
  textId,
  label,
  url,
}: CreateExternalTextLinkProps): Promise<ActionState<ExternalLink>> => {
  let externalLink: any = null;

  try {
    externalLink = await createExternalTextLink({
      textId,
      label,
      url,
    });

    return {
      code: StatusCode.Created,
      message: "External link created successfully",
      data: externalLink as ExternalLink,
      success: true,
    };
  } catch (error) {
    console.error("Error creating external link:", error);
    return {
      code: StatusCode.InternalServerError,
      message: "Something went wrong while creating the external link",
      error: error as Error,
      success: false,
    };
  } finally {
    revalidatePath(`/project/${externalLink?.text?.section?.projectId}`);
  }
};

export const updateExternalLinkAction = async ({
  id,
  label,
  url,
}: UpdateExternalLinkProps): Promise<ActionState<ExternalLink>> => {
  let externalLink: any = null;

  try {
    const isExternalLinkExists = await isExternalLink(id);

    if (!isExternalLinkExists) {
      return {
        code: StatusCode.NotFound,
        message: "External link not found",
        success: false,
      };
    }

    externalLink = await updateExternalLink({
      id,
      label,
      url,
    });

    return {
      code: StatusCode.Ok,
      message: "External link updated successfully",
      data: externalLink as ExternalLink,
      success: true,
    };
  } catch (error) {
    console.error("Error updating external link:", error);
    return {
      code: StatusCode.InternalServerError,
      message: "Something went wrong while updating the external link",
      error: error as Error,
      success: false,
    };
  } finally {
    revalidatePath(`/project/${externalLink?.section?.projectId}`);
  }
};

export const deleteExternalLinkAction = async (
  id: ExternalLink["id"]
): Promise<ActionState<ExternalLink>> => {
  let externalLink: any = null;

  try {
    const isExternalLinkExists = await isExternalLink(id);

    if (!isExternalLinkExists) {
      return {
        code: StatusCode.NotFound,
        message: "External link not found",
        success: false,
      };
    }

    externalLink = await deleteExternalLink(id);

    return {
      code: StatusCode.Ok,
      message: "External link deleted successfully",
      data: externalLink as ExternalLink,
      success: true,
    };
  } catch (error) {
    console.error("Error deleting external link:", error);
    return {
      code: StatusCode.InternalServerError,
      message: "Something went wrong while deleting the external link",
      error: error as Error,
      success: false,
    };
  } finally {
    revalidatePath(`/project/${externalLink?.section?.projectId}`);
  }
};
